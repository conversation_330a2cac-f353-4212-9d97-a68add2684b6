import { WebSocket } from 'ws';
import { TranscriptionStatus } from './TranscriptionService';

export interface AudioChunk {
  data: Uint8Array;
  isLastChunk: boolean;
}

export class RealtimeConnectionManager {
  private ws: WebSocket | null = null;
  private isConnected = false;
  private transcript = '';
  private lastPartialTranscript = '';
  private statusListeners: ((status: TranscriptionStatus, message?: string) => void)[] = [];
  private transcriptionListeners: ((text: string, isFinal: boolean) => void)[] = [];
  private readyListeners: (() => void)[] = [];

  // Add new properties for tracking processing state
  private processedDuration: number = 0;
  private allChunksProcessed: boolean = false;
  private finalDurationReceived: boolean = false;

  // Circuit breaker state
  private circuitState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private nextRetryTime = 0;
  private readonly failureThreshold = 3;
  private readonly retryTimeoutBase = 2000; // 2 seconds
  private readonly maxRetryTimeout = 30000; // 30 seconds

  private connectionAttempts = 0;
  private readonly MAX_CONNECTION_ATTEMPTS = 3;

  // Add heartbeat mechanism
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private readonly HEARTBEAT_INTERVAL = 15000; // 15 seconds
  private lastMessageReceivedTime: number = 0;
  private readonly CONNECTION_TIMEOUT = 30000; // 30 seconds
  private connectionMonitorInterval: NodeJS.Timeout | null = null;

  constructor(
    private apiKey: string,
    private service: string,
    private model: string,
    private language: string,
    private sampleRate: number = 16000 // Default to 16kHz if not specified
  ) {
    // Initialize state - but preserve transcript data if this is a reconnection
    this.ws = null;
    this.isConnected = false;

    // Only reset transcript data if it's truly empty (new session)
    if (!this.transcript) {
      this.transcript = '';
    }
    if (!this.lastPartialTranscript) {
      this.lastPartialTranscript = '';
    }

    this.processedDuration = 0;
    this.allChunksProcessed = false;
    this.finalDurationReceived = false;
    this.connectionAttempts = 0;
    this.statusListeners = [];
    this.transcriptionListeners = [];
    this.readyListeners = [];

    console.log(`VoiceHype: Created RealtimeConnectionManager with service: ${service}, model: ${model}, language: ${language}, sample rate: ${sampleRate}Hz`);
    console.log(`VoiceHype: Preserving existing transcript data: ${this.transcript.length} chars, partial: ${this.lastPartialTranscript.length} chars`);
  }

  // Add new TranscriptionStatus for duration and processing
  public static readonly UsageReceived = 'usage_received';
  public static readonly AllChunksProcessed = 'all_chunks_processed';

  // Connect to the WebSocket before recording starts
  public async connect(): Promise<void> {
    this.emitStatus(TranscriptionStatus.ConnectingWebSocket);
    this.connectionAttempts = 0;
    this.lastMessageReceivedTime = Date.now(); // Initialize last message time

    return this.attemptConnection();
  }

  private async attemptConnection(): Promise<void> {
    // Check circuit breaker state
    if (this.circuitState === 'open') {
      if (Date.now() < this.nextRetryTime) {
        throw new Error('Service unavailable - circuit breaker open');
      }
      this.circuitState = 'half-open';
    }

    this.connectionAttempts++;
    console.log(`VoiceHype: Connection attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS}`);

    // Clear any existing intervals
    this.clearHeartbeatAndMonitor();

    return new Promise((resolve, reject) => {
      // Record start time for retry timeout calculation
      const startTime = Date.now();
      const serverUrl = '***************:3001';

      // Properly encode API key and parameters to ensure Windows compatibility
      const encodedApiKey = encodeURIComponent(this.apiKey);
      const encodedLanguage = encodeURIComponent(this.language);

      // Use the new Express.js server URL (HTTP since SSL not yet implemented)
      const wsUrl = `ws://${serverUrl}/realtime?apiKey=${encodedApiKey}&service=${this.service}&model=${this.model}&language=${encodedLanguage}`;

      // Log the URL (hide API key)
      const safeUrl = wsUrl.replace(encodedApiKey, '***API_KEY_HIDDEN***');
      console.log(`VoiceHype: Connecting to real-time transcription WebSocket: ${safeUrl}`);

      try {
        // Use a consistent WebSocket configuration for all platforms
        this.ws = new WebSocket(wsUrl, {
          headers: {
            'User-Agent': 'VoiceHype-VSCode-Extension'
          },
          handshakeTimeout: 20 * 60 * 1000 // Set it to 20 minutes
        });

        console.log('VoiceHype: Created WebSocket connection to Express.js server with 20m handshake timeout');
      } catch (error) {
        console.error('VoiceHype: Error creating WebSocket:', error);
        return this.handleConnectionError(error, resolve, reject);
      }

      // Set up a connection timeout
      const connectionTimeout = setTimeout(() => {
        console.error('VoiceHype: Connection timeout after 30 seconds');
        this.emitStatus(TranscriptionStatus.Error, 'Connection timeout after 30 seconds');

        if (this.ws) {
          this.ws.terminate();
          this.ws = null;
        }

        this.handleConnectionError(new Error('Connection timeout'), resolve, reject);
      }, 30000); // Increase to 30 seconds

      this.ws.on('open', () => {
        const openTime = new Date().toISOString();
        console.log(`VoiceHype: [DEBUG] WebSocket connection opened at ${openTime}`);
        console.log(`VoiceHype: [DEBUG] WebSocket state after open: readyState=${this.ws?.readyState}, isConnected=${this.isConnected}`);

        this.emitStatus(TranscriptionStatus.WebSocketConnected);
        clearTimeout(connectionTimeout);
        this.lastMessageReceivedTime = Date.now(); // Update last message time

        console.log(`VoiceHype: [DEBUG] Setting up heartbeat and connection monitoring`);
        // Set up heartbeat to keep the connection alive
        this.setupHeartbeatAndMonitor();

        // Give the server a moment to initialize the session
        console.log(`VoiceHype: [DEBUG] Waiting 1000ms for server to initialize session`);
        setTimeout(() => {
          if (!this.isConnected) {
            console.log(`VoiceHype: [DEBUG] Connection ready after timeout, notifying ${this.readyListeners.length} listeners`);
            this.isConnected = true;
            this.readyListeners.forEach(listener => listener());
            resolve();
          } else {
            console.log(`VoiceHype: [DEBUG] Connection was already marked as ready, not notifying listeners again`);
          }
        }, 1000);
      });

      this.ws.on('message', (data: Buffer) => {
        try {
          // Update last message received time for connection monitoring
          const now = Date.now();
          const timeSinceLastMessage = now - this.lastMessageReceivedTime;
          this.lastMessageReceivedTime = now;

          // Log message receipt with timing information
          console.log(`VoiceHype: [DEBUG] Message received after ${timeSinceLastMessage}ms, size: ${data.length} bytes`);

          // Handle binary audio data which shouldn't be parsed
          if (data instanceof Buffer) {
            // Check if this looks like binary audio data (not starting with "{")
            const firstByte = data.length > 0 ? data[0] : 0;
            // '{' character is ASCII 123
            if (firstByte !== 123) {
              console.log(`VoiceHype: [DEBUG] Received binary data of ${data.length} bytes, not trying to parse`);
              return; // Skip processing binary data
            }
          }

          // Check if data is valid before trying to convert to string
          if (!data || data.length === 0) {
            console.log('VoiceHype: [DEBUG] Received empty message data');
            return;
          }

          // Try to safely convert to string and log raw data in case of errors
          let message: string;
          try {
            message = data.toString();
            // Verify it looks like JSON before proceeding
            if (!message.startsWith('{') && !message.startsWith('[')) {
              console.log(`VoiceHype: [DEBUG] Received non-JSON data, length: ${message.length} bytes`);
              return;
            }

            // Log only the first part to avoid huge logs
            console.log(`VoiceHype: [DEBUG] WebSocket message received (${message.length} bytes): ${message.substring(0, 200)}${message.length > 200 ? '...' : ''}`);
          } catch (stringError) {
            console.error(`VoiceHype: [DEBUG] Error converting message to string: ${stringError instanceof Error ? stringError.message : String(stringError)}`);
            console.log(`VoiceHype: [DEBUG] Raw message data: type=${typeof data}, isBuffer=${data instanceof Buffer}, length=${data instanceof Buffer ? data.length : 'unknown'}`);

            // Try to show the first few bytes if it's a buffer
            if (data instanceof Buffer && data.length > 0) {
              try {
                // Use Buffer.subarray instead of slice (which is deprecated)
                const preview = Buffer.from(data.subarray(0, Math.min(50, data.length))).toString('hex');
                console.log(`VoiceHype: [DEBUG] First ${Math.min(50, data.length)} bytes as hex: ${preview}`);
              } catch (bufferError) {
                console.error(`VoiceHype: [DEBUG] Could not convert buffer to hex: ${bufferError instanceof Error ? bufferError.message : String(bufferError)}`);
              }
            }
            return;
          }

          // Safely try to parse JSON
          let parsedData: any;
          try {
            parsedData = JSON.parse(message);
            console.log(`VoiceHype: [DEBUG] Successfully parsed JSON message of type: ${parsedData.type || parsedData.message_type || 'unknown'}`);
          } catch (jsonError) {
            console.error(`VoiceHype: [DEBUG] Error parsing JSON message: ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
            console.log(`VoiceHype: [DEBUG] Raw message content (first 200 chars): ${message.substring(0, 200)}${message.length > 200 ? '...' : ''}`);
            return;
          }

          // Extra defensive check for parsedData
          if (!parsedData) {
            console.log('VoiceHype: [DEBUG] Parsed data is null or undefined');
            return;
          }

          // Handle heartbeat responses
          if (parsedData.type === 'pong' || parsedData.message_type === 'pong') {
            const timeSinceLastHeartbeat = Date.now() - this.lastMessageReceivedTime;
            console.log(`VoiceHype: [DEBUG] Received heartbeat response after ${timeSinceLastHeartbeat}ms, connection state: ${this.ws?.readyState}`);
            return;
          }

          // Handle messages based on type field
          const messageType = parsedData.type || parsedData.message_type || 'unknown';
          console.log(`VoiceHype: Processing message type: ${messageType}`);

          // Add handlers for duration and finalization messages
          if (messageType === 'duration') {
            console.log(`VoiceHype: Received duration information: ${parsedData.duration}s, sessionId: ${parsedData.sessionId || 'unknown'}`);
            this.processedDuration = parsedData.duration || 0;
            this.finalDurationReceived = true;
            this.emitStatus(TranscriptionStatus.UsageReceived, `Duration: ${this.processedDuration}s`);
          }
          else if (messageType === 'CompleteTranscript') {
            // Handle the complete transcript from AssemblyAI (at the end of recording)
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              // Only update transcript if we don't already have a complete one
              // This prevents overwriting accumulated transcript data
              if (this.transcript.trim() === '') {
                this.transcript = text.trim();
                console.log(`VoiceHype: Complete transcription received (first): "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`);
              } else {
                // If we already have transcript data, append new content if it's different
                const existingText = this.transcript.trim();
                const newText = text.trim();
                if (newText.length > existingText.length && newText.includes(existingText)) {
                  // New text contains existing text - use the longer version
                  this.transcript = newText;
                  console.log(`VoiceHype: Complete transcription updated (longer): "${newText.substring(0, 100)}${newText.length > 100 ? '...' : ''}"`);
                } else if (!existingText.includes(newText) && newText !== existingText) {
                  // Append if it's genuinely new content
                  this.transcript = existingText + ' ' + newText;
                  console.log(`VoiceHype: Complete transcription appended: "${newText.substring(0, 100)}${newText.length > 100 ? '...' : ''}"`);
                } else {
                  console.log(`VoiceHype: Complete transcription ignored (duplicate): "${newText.substring(0, 100)}${newText.length > 100 ? '...' : ''}"`);
                }
              }

              this.emitStatus(TranscriptionStatus.TranscriptionReceived, 'Complete transcript received');

              // Mark chunks as processed since we got the final transcript
              this.allChunksProcessed = true;
              this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Complete transcript processed');
              this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');

              // Notify listeners with the current complete transcript
              this.transcriptionListeners.forEach(listener => listener(this.transcript, true));
            } else {
              console.log('VoiceHype: Received empty CompleteTranscript message');
            }
          }
          else if (messageType === 'finalized') {
            console.log(`VoiceHype: Session finalized: ${parsedData.message || 'No message'}, sessionId: ${parsedData.sessionId || 'unknown'}`);
            this.allChunksProcessed = true;
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'All audio processed');

            // If we're done processing, emit the final status
            if (this.finalDurationReceived) {
              this.emitStatus(TranscriptionStatus.TranscriptionComplete, `Completed: ${this.processedDuration.toFixed(1)}s audio`);
            }
          }
          else if (messageType === 'connected') {
            console.log('VoiceHype: Real-time transcription session established:', parsedData.sessionId || 'no-session-id');
            this.emitStatus(TranscriptionStatus.SessionEstablished, `Session ID: ${parsedData.sessionId || 'unknown'}`);

            if (!this.isConnected) {
              console.log('VoiceHype: Setting connected state and notifying listeners');
              this.isConnected = true;

              // Notify that we're ready to record
              this.readyListeners.forEach(listener => listener());
              resolve();
            } else {
              console.log('VoiceHype: Already connected, ignoring duplicate connected message');
            }
          } else if (messageType === 'final') {
            // Handle final transcriptions from other services
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              // Check if this text is already included in our transcript to avoid duplicates
              const existingText = this.transcript.trim();
              const newText = text.trim();

              if (existingText === '' || !existingText.includes(newText)) {
                // Only append if it's new content
                this.transcript += (existingText ? ' ' : '') + newText;
                console.log(`VoiceHype: Final transcription appended: "${newText}"`);
                this.emitStatus(TranscriptionStatus.TranscriptionReceived, newText);
                this.transcriptionListeners.forEach(listener => listener(newText, true));
              } else {
                console.log(`VoiceHype: Final transcription ignored (duplicate): "${newText}"`);
              }
            }
          } else if (messageType === 'partial' || messageType === 'PartialTranscript') {
            // Save partial transcriptions as fallback in case final transcript is missed
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              const newPartial = text.trim();

              // Keep the longest/most complete partial transcript as fallback
              if (newPartial.length > this.lastPartialTranscript.length) {
                this.lastPartialTranscript = newPartial;

                // Only log occasionally to reduce noise
                if (Math.random() < 0.05) { // Log ~5% of partials
                  console.log(`VoiceHype: Updated partial transcription fallback: "${newPartial.substring(0, 50)}${newPartial.length > 50 ? '...' : ''}"`);
                }
              }
            }
          } else if (messageType === 'error') {
            console.error('VoiceHype: Real-time transcription error:', parsedData.message || 'Unknown error');
            this.emitStatus(TranscriptionStatus.Error, parsedData.message || 'Unknown error');
          } else if (messageType === 'timeout') {
            console.log('VoiceHype: Real-time transcription timeout:', parsedData.message || 'Unknown timeout');
            this.emitStatus(TranscriptionStatus.Error, `Timeout: ${parsedData.message || 'Unknown reason'}`);
          } else if (messageType === 'service_disconnected') {
            console.log('VoiceHype: Real-time transcription service disconnected:', parsedData.reason || 'Unknown reason');
            this.emitStatus(TranscriptionStatus.Error, `Service disconnected: ${parsedData.reason || 'Unknown reason'}`);
            this.isConnected = false;
          } else {
            // Handle unknown message types
            console.log('VoiceHype: Received unknown message type:', messageType, 'Content:', JSON.stringify(parsedData).substring(0, 200));
          }
        } catch (error) {
          console.error('VoiceHype: Error handling WebSocket message:', error);
          console.log('VoiceHype: Original data type:', typeof data, 'Is Buffer:', data instanceof Buffer);

          if (data instanceof Buffer) {
            try {
              // Use Buffer.subarray instead of slice (which is deprecated)
              const preview = Buffer.from(data.subarray(0, Math.min(50, data.length))).toString('hex');
              console.log(`VoiceHype: [DEBUG] First ${Math.min(50, data.length)} bytes as hex: ${preview}`);
            } catch (bufferError) {
              console.error(`VoiceHype: [DEBUG] Could not convert buffer to hex: ${bufferError instanceof Error ? bufferError.message : String(bufferError)}`);
            }
          }
        }
      });

      this.ws.on('error', (error) => {
        const errorTime = new Date().toISOString();
        console.error(`VoiceHype: [DEBUG] WebSocket error at ${errorTime}:`, error);
        console.error(`VoiceHype: [DEBUG] WebSocket state during error: readyState=${this.ws?.readyState}, isConnected=${this.isConnected}`);
        console.error(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);

        this.emitStatus(TranscriptionStatus.Error, `WebSocket error: ${error.message}`);
        this.isConnected = false;
        clearTimeout(connectionTimeout);

        // Clear heartbeat and monitor
        this.clearHeartbeatAndMonitor();

        // Try to reconnect if this was the initial connection attempt
        this.handleConnectionError(error, resolve, reject);
      });

      this.ws.on('close', (code, reason) => {
        const closeTime = new Date().toISOString();
        console.log(`VoiceHype: [DEBUG] WebSocket connection closed at ${closeTime} with code ${code}, reason: ${reason || 'None provided'}`);
        console.log(`VoiceHype: [DEBUG] WebSocket state during close: isConnected=${this.isConnected}`);
        console.log(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);
        console.log(`VoiceHype: [DEBUG] Transcript status: length=${this.transcript.trim().length}, lastPartialLength=${this.lastPartialTranscript.trim().length}`);

        // If we have no final transcript but have partial ones, use the last partial
        if (this.transcript.trim() === '' && this.lastPartialTranscript.trim() !== '') {
          console.log(`VoiceHype: [DEBUG] No final transcript received, using last partial: "${this.lastPartialTranscript}"`);
          this.transcript = this.lastPartialTranscript;
          // Notify listeners that we're using the partial as final
          this.transcriptionListeners.forEach(listener => listener(this.lastPartialTranscript, true));
        } else if (this.transcript.trim() !== '') {
          console.log(`VoiceHype: [DEBUG] Final transcript available: "${this.transcript.substring(0, 100)}${this.transcript.length > 100 ? '...' : ''}"`);
        } else {
          console.log(`VoiceHype: [DEBUG] No transcript data available (neither final nor partial)`);
        }

        this.isConnected = false;

        // Clear heartbeat and monitor
        this.clearHeartbeatAndMonitor();

        // Log close code meaning
        if (code === 1000) {
          console.log(`VoiceHype: [DEBUG] Normal closure (1000): The connection successfully completed the purpose for which it was created`);
        } else if (code === 1001) {
          console.log(`VoiceHype: [DEBUG] Going Away (1001): The endpoint is going away (e.g., server shutdown or browser page navigation)`);
        } else if (code === 1002) {
          console.log(`VoiceHype: [DEBUG] Protocol Error (1002): The endpoint terminated the connection due to a protocol error`);
        } else if (code === 1003) {
          console.log(`VoiceHype: [DEBUG] Unsupported Data (1003): The connection was terminated because the endpoint received data of a type it cannot accept`);
        } else if (code === 1005) {
          console.log(`VoiceHype: [DEBUG] No Status Received (1005): No status code was provided even though one was expected`);
        } else if (code === 1006) {
          console.log(`VoiceHype: [DEBUG] Abnormal Closure (1006): The connection was closed abnormally (e.g., without sending or receiving a Close control frame)`);
        } else if (code === 1007) {
          console.log(`VoiceHype: [DEBUG] Invalid frame payload data (1007): The endpoint terminated the connection because a message contained inconsistent data`);
        } else if (code === 1008) {
          console.log(`VoiceHype: [DEBUG] Policy Violation (1008): The endpoint terminated the connection because it received a message that violates its policy`);
        } else if (code === 1009) {
          console.log(`VoiceHype: [DEBUG] Message Too Big (1009): The endpoint terminated the connection because a data frame was too large`);
        } else if (code === 1010) {
          console.log(`VoiceHype: [DEBUG] Missing Extension (1010): The client terminated the connection because it expected the server to negotiate one or more extensions`);
        } else if (code === 1011) {
          console.log(`VoiceHype: [DEBUG] Internal Error (1011): The server terminated the connection because it encountered an unexpected condition`);
        } else if (code === 1012) {
          console.log(`VoiceHype: [DEBUG] Service Restart (1012): The server is restarting`);
        } else if (code === 1013) {
          console.log(`VoiceHype: [DEBUG] Try Again Later (1013): The server is terminating the connection due to a temporary condition`);
        } else if (code === 1014) {
          console.log(`VoiceHype: [DEBUG] Bad Gateway (1014): The server was acting as a gateway or proxy and received an invalid response from an upstream server`);
        } else if (code === 1015) {
          console.log(`VoiceHype: [DEBUG] TLS Handshake (1015): The connection was closed due to a failure to perform a TLS handshake`);
        } else {
          console.log(`VoiceHype: [DEBUG] Unknown close code (${code}): The connection was closed for an unknown reason`);
        }

        // Attempt to reconnect if this wasn't a normal closure and we're still recording
        if (code !== 1000 && code !== 1001) {
          console.log(`VoiceHype: [DEBUG] Abnormal closure (${code}), attempting to reconnect...`);
          this.attemptReconnection();
        } else {
          console.log(`VoiceHype: [DEBUG] Normal closure (${code}), not attempting to reconnect`);
          this.emitStatus(TranscriptionStatus.TranscriptionComplete);
        }
      });
    });
  }

  private handleConnectionError(error: any, resolve: Function, reject: Function): void {
    // Record failure
    this.failureCount++;
    this.lastFailureTime = Date.now();

    // Check if we should trip the circuit breaker
    if (this.failureCount >= this.failureThreshold) {
      this.circuitState = 'open';
      const retryTimeout = this.calculateRetryTimeout();
      this.nextRetryTime = Date.now() + retryTimeout;
      console.error(`VoiceHype: Circuit breaker tripped - service unavailable for ${retryTimeout}ms`);
      this.emitStatus(TranscriptionStatus.Error, `Service unavailable - retrying in ${Math.round(retryTimeout/1000)}s`);
      reject(error);
      return;
    }

    if (this.connectionAttempts < this.MAX_CONNECTION_ATTEMPTS) {
      console.log(`VoiceHype: Connection failed, retrying (${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS})...`);

      // Calculate retry timeout with exponential backoff
      const retryTimeout = this.calculateRetryTimeout();
      console.log(`VoiceHype: Waiting ${retryTimeout}ms before retry...`);

      // Add a delay before retrying
      setTimeout(() => {
        this.attemptConnection()
          .then(() => resolve())
          .catch(err => reject(err));
      }, retryTimeout);
    } else {
      console.error('VoiceHype: Failed to connect after multiple attempts:', error);
      this.emitStatus(TranscriptionStatus.Error, `Connection failed: ${error.message}`);
      reject(error);
    }
  }

  private calculateRetryTimeout(): number {
    const timeout = Math.min(
      this.retryTimeoutBase * Math.pow(2, this.failureCount),
      this.maxRetryTimeout
    );
    return timeout;
  }

  // Send audio chunk as it's captured
  public sendAudioChunk(chunk: AudioChunk): void {
    if (!this.ws || !this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      console.log('VoiceHype: Cannot send audio chunk: WebSocket not connected');
      return;
    }

    try {
      // Get platform to determine how to send data
      const platform = process.platform;

      // Only log occasional chunks to avoid flooding logs
      if (Math.random() < 0.05) {
        console.log(`VoiceHype: Sending audio chunk of ${chunk.data.length} bytes, isLastChunk: ${chunk.isLastChunk}, platform: ${platform}`);
      }

      // Signal that audio is being streamed
      this.emitStatus(TranscriptionStatus.StreamingAudio);

      if (platform === 'linux') {
        // For Linux, convert to ArrayBuffer before sending
        const arrayBuffer = chunk.data.buffer.slice(
          chunk.data.byteOffset,
          chunk.data.byteOffset + chunk.data.byteLength
        );
        this.ws.send(arrayBuffer);
      } else {
        // For Windows and macOS, send the Buffer directly
        this.ws.send(Buffer.from(chunk.data));
      }

      if (chunk.isLastChunk) {
        console.log('VoiceHype: Sending final audio chunk, closing session');
        this.emitStatus(TranscriptionStatus.StreamingAudio, 'Sending final audio...');

        // Give the server more time to process before sending close message
        setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('VoiceHype: Sending close message to request final transcript');
            this.ws.send(JSON.stringify({
              type: 'close',
              requestFinalTranscript: true // Add explicit request for final transcript
            }));

            // Update status to show we're waiting for server to finish
            this.emitStatus(TranscriptionStatus.StreamingAudio, 'Waiting for final transcription...');

            // Wait longer (10s) for the final transcript before giving up
            setTimeout(() => {
              if (!this.allChunksProcessed) {
                console.log('VoiceHype: Auto-marking chunks as processed after waiting 10 seconds');
                this.allChunksProcessed = true;
                this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Finalizing with available transcript');
              }
            }, 10000); // Wait 10 seconds to mark chunks as processed (up from 5s)
          }
        }, 1000); // Keep the 1 second delay before sending close
      }
    } catch (error) {
      console.error('VoiceHype: Error sending audio chunk:', error);
      this.emitStatus(TranscriptionStatus.Error,
        `Error sending audio: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Set up heartbeat and connection monitoring
  private setupHeartbeatAndMonitor(): void {
    // Clear any existing intervals
    this.clearHeartbeatAndMonitor();

    console.log(`VoiceHype: [DEBUG] Setting up heartbeat (${this.HEARTBEAT_INTERVAL}ms) and connection monitor`);

    // Set up heartbeat to keep the connection alive
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        const timeSinceLastMessage = Date.now() - this.lastMessageReceivedTime;
        console.log(`VoiceHype: [DEBUG] Sending heartbeat ping. WebSocket state: ${this.ws.readyState}, ${timeSinceLastMessage}ms since last message`);
        try {
          this.ws.send(JSON.stringify({
            type: 'ping',
            timestamp: Date.now(),
            debug: {
              timeSinceLastMessage,
              connectionState: this.ws.readyState
            }
          }));
        } catch (error) {
          console.error(`VoiceHype: [DEBUG] Error sending heartbeat: ${error instanceof Error ? error.message : String(error)}`);

          // Log detailed WebSocket state
          console.error(`VoiceHype: [DEBUG] WebSocket state during error: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
        }
      } else {
        const state = this.ws ? this.ws.readyState : 'null';
        console.log(`VoiceHype: [DEBUG] WebSocket not open (state: ${state}), clearing heartbeat`);
        this.clearHeartbeatAndMonitor();
      }
    }, this.HEARTBEAT_INTERVAL);

    // Set up connection monitor to detect stalled connections
    this.connectionMonitorInterval = setInterval(() => {
      const now = Date.now();
      const timeSinceLastMessage = now - this.lastMessageReceivedTime;

      console.log(`VoiceHype: [DEBUG] Connection monitor check - ${timeSinceLastMessage}ms since last message, timeout threshold: ${this.CONNECTION_TIMEOUT}ms`);

      // Log WebSocket state
      if (this.ws) {
        console.log(`VoiceHype: [DEBUG] WebSocket state: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      } else {
        console.log(`VoiceHype: [DEBUG] WebSocket is null, isConnected=${this.isConnected}`);
      }

      if (timeSinceLastMessage > this.CONNECTION_TIMEOUT) {
        console.error(`VoiceHype: [DEBUG] Connection appears stalled (${timeSinceLastMessage}ms since last message, threshold: ${this.CONNECTION_TIMEOUT}ms)`);
        this.emitStatus(TranscriptionStatus.Error, `Connection stalled - no response from server for ${Math.round(timeSinceLastMessage/1000)}s`);

        // Attempt to reconnect
        this.attemptReconnection();
      }
    }, 5000); // Check every 5 seconds for more frequent monitoring
  }

  // Clear heartbeat and connection monitoring intervals
  private clearHeartbeatAndMonitor(): void {
    if (this.heartbeatInterval !== null) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.connectionMonitorInterval !== null) {
      clearInterval(this.connectionMonitorInterval);
      this.connectionMonitorInterval = null;
    }
  }

  // Attempt to reconnect to the WebSocket
  private attemptReconnection(): void {
    const reconnectStartTime = Date.now();
    console.log(`VoiceHype: [DEBUG] Attempting to reconnect to WebSocket at ${new Date().toISOString()}`);

    // Log detailed connection state before reconnection
    if (this.ws) {
      console.log(`VoiceHype: [DEBUG] Current WebSocket state before reconnection: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      console.log(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);
    } else {
      console.log(`VoiceHype: [DEBUG] WebSocket is null before reconnection, isConnected=${this.isConnected}`);
    }

    // Close existing connection if it's still open
    if (this.ws) {
      try {
        console.log(`VoiceHype: [DEBUG] Closing existing WebSocket connection with readyState=${this.ws.readyState}`);
        this.ws.close();
      } catch (error) {
        console.error(`VoiceHype: [DEBUG] Error closing existing WebSocket: ${error instanceof Error ? error.message : String(error)}`);
      }
      this.ws = null;
    }

    // Clear existing intervals
    this.clearHeartbeatAndMonitor();

    // Attempt to reconnect
    this.emitStatus(TranscriptionStatus.Reconnecting, `Reconnecting to transcription service...`);
    this.connect().then(() => {
      const reconnectTime = Date.now() - reconnectStartTime;
      console.log(`VoiceHype: [DEBUG] Successfully reconnected after ${reconnectTime}ms`);

      // Log new connection state
      if (this.ws) {
        console.log(`VoiceHype: [DEBUG] New WebSocket state after reconnection: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      } else {
        console.log(`VoiceHype: [DEBUG] WebSocket is null after reconnection (unexpected), isConnected=${this.isConnected}`);
      }

      this.emitStatus(TranscriptionStatus.Reconnected, `Reconnected successfully after ${Math.round(reconnectTime/1000)}s`);
    }).catch(error => {
      const reconnectTime = Date.now() - reconnectStartTime;
      console.error(`VoiceHype: [DEBUG] Failed to reconnect after ${reconnectTime}ms: ${error instanceof Error ? error.message : String(error)}`);
      this.emitStatus(TranscriptionStatus.Error, `Failed to reconnect to transcription service: ${error instanceof Error ? error.message : String(error)}`);
    });
  }

  /**
   * Closes the WebSocket connection and cleans up resources
   */
  public close() {
    console.log("VoiceHype: Closing WebSocket connection");

    // Clear heartbeat and monitor
    this.clearHeartbeatAndMonitor();

    if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
      // Report that we're processing any final chunks
      if (this.isConnected && !this.allChunksProcessed) {
        console.log('VoiceHype: Waiting for final transcript before closing');
        // Don't close the connection immediately if we're still waiting for the final transcript
        // Instead, send a close message and let the server close the connection
        if (this.ws.readyState === WebSocket.OPEN) {
          console.log('VoiceHype: Sending close message to get final transcript');
          this.ws.send(JSON.stringify({
            type: 'close',
            requestFinalTranscript: true
          }));

          // Don't close the connection immediately - let the server close it
          // after it sends the final transcript
          return;
        }
      }

      // If we're not waiting for the final transcript, close normally
      this.ws.close();
      this.ws = null;
    }

    this.isConnected = false;
  }

  // Get the current transcript
  public getTranscript(): string {
    return this.transcript.trim();
  }

  // Event listeners
  public onStatusChange(listener: (status: TranscriptionStatus, message?: string) => void): void {
    this.statusListeners.push(listener);
  }

  public onTranscriptionUpdate(listener: (text: string, isFinal: boolean) => void): void {
    this.transcriptionListeners.push(listener);
  }

  public onReady(listener: () => void): void {
    this.readyListeners.push(listener);

    // If already connected, call immediately
    if (this.isConnected) {
      listener();
    }
  }

  private emitStatus(status: TranscriptionStatus, message?: string): void {
    console.log(`VoiceHype: Status update - ${status}${message ? `: ${message}` : ''}`);
    this.statusListeners.forEach(listener => listener(status, message));
  }

  // Add a method to get the processed duration
  public getProcessedDuration(): number {
    return this.processedDuration;
  }

  // Add a method to check if all chunks have been processed
  public areAllChunksProcessed(): boolean {
    return this.allChunksProcessed;
  }

  // Add a method to wait for all chunks to be processed with timeout
  public async waitForProcessing(timeoutMs: number = 5000): Promise<boolean> {
    if (this.allChunksProcessed) {
      console.log('VoiceHype: All chunks already processed, no need to wait');
      return true;
    }

    console.log(`VoiceHype: Waiting for all chunks to be processed (max ${timeoutMs}ms)...`);
    this.emitStatus(TranscriptionStatus.StreamingAudio, 'Finalizing transcription...');

    return new Promise<boolean>((resolve) => {
      const startTime = Date.now();
      const timeout = setTimeout(() => {
        const elapsed = Date.now() - startTime;
        console.log(`VoiceHype: Timeout after ${elapsed}ms waiting for all chunks to be processed`);
        // Even if we time out, return true - we'll use whatever transcript we have
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Proceeding with available transcript');
        resolve(true);
      }, timeoutMs);

      // Keep track of how long we've been waiting
      let waitCount = 0;

      const checkProcessed = () => {
        if (this.allChunksProcessed) {
          const elapsed = Date.now() - startTime;
          console.log(`VoiceHype: All chunks have been processed after ${elapsed}ms`);
          clearTimeout(timeout);
          resolve(true);
          return;
        }

        // Report waiting status periodically
        waitCount++;
        if (waitCount % 3 === 0) { // Every 1.5 seconds (500ms * 3)
          const elapsed = Date.now() - startTime;
          const message = `Finalizing... (${(elapsed/1000).toFixed(1)}s)`;
          console.log(`VoiceHype: ${message}`);
          this.emitStatus(TranscriptionStatus.StreamingAudio, message);

          // If we've been waiting more than 3 seconds, just proceed anyway
          if (elapsed > 3000) {
            console.log('VoiceHype: Been waiting for 3s, proceeding with available transcript');
            clearTimeout(timeout);
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Proceeding with available transcript');
            resolve(true);
            return;
          }
        }

        // Check every 500ms
        setTimeout(checkProcessed, 500);
      };

      checkProcessed();
    });
  }

  // Get a summary of the transcription process
  public getTranscriptionSummary(): string {
    const transcriptLength = this.transcript.trim().length;
    const words = this.transcript.trim().split(/\s+/).length;

    if (transcriptLength === 0) {
      return 'No transcription available yet.';
    }

    let summary = `Transcribed ${words} words`;

    if (this.processedDuration > 0) {
      summary += ` from ${this.processedDuration.toFixed(1)}s of audio`;
      const wordsPerSecond = words / this.processedDuration;
      summary += ` (${wordsPerSecond.toFixed(1)} words/sec)`;
    }

    return summary;
  }

  // Method to update sample rate
  public updateSampleRate(newSampleRate: number): void {
    if (this.sampleRate === newSampleRate) {
      return; // No change needed
    }

    console.log(`VoiceHype: Updating sample rate from ${this.sampleRate}Hz to ${newSampleRate}Hz`);
    this.sampleRate = newSampleRate;
  }

  // Method to manually reset transcript data (for new recording sessions)
  public resetTranscriptData(): void {
    console.log(`VoiceHype: Manually resetting transcript data (was ${this.transcript.length} chars)`);
    this.transcript = '';
    this.lastPartialTranscript = '';
    this.processedDuration = 0;
    this.allChunksProcessed = false;
    this.finalDurationReceived = false;
  }

  // Method to check if we have any transcript data
  public hasTranscriptData(): boolean {
    return this.transcript.trim().length > 0 || this.lastPartialTranscript.trim().length > 0;
  }
}